import type { APIRoute } from 'astro';
import { supabase } from '../../../lib/supabase';

type BookingCount = {
  date: string;
  time: string;
};

type OpenDay = {
  date: string;
};

type TimeSlot = {
  id: string;
  date: string;
  time: string;
  max_capacity: number;
};


export const GET: APIRoute = async () => {
  // Step 1: Get open days
    const { data: openDays, error: openError } = await supabase
    .from('open_days')
    .select('date')
    .eq('is_open', true)
    .order('date', { ascending: true })
    .returns<OpenDay[]>();

  if (openError || !openDays) {
    return new Response(JSON.stringify({ error: openError?.message }), { status: 500 });
  }

  // Step 2: Get time slots for open days
    const { data: slots, error: slotError } = await supabase
    .from('time_slots')
    .select('id, date, time, max_capacity')
    .in('date', openDays.map(d => d.date))
    .returns<TimeSlot[]>();

  if (slotError || !slots) {
    return new Response(JSON.stringify({ error: slotError?.message }), { status: 500 });
  }

  // Step 3: Get bookings to count how full each slot is
const { data: bookings, error: bookingError } = await supabase
  .from('bookings')
  .select('date, time')
  .returns<BookingCount[]>();

  if (bookingError || !bookings) {
    return new Response(JSON.stringify({ error: bookingError?.message }), { status: 500 });
  }

  // Step 4: Organize availability
  const bookingsMap = new Map<string, number>();
  for (const b of bookings) {
    const key = `${b.date}|${b.time}`;
    bookingsMap.set(key, (bookingsMap.get(key) || 0) + 1);
  }

  const availability = openDays.map(day => {
    const daySlots = slots
      .filter(slot => slot.date === day.date)
      .map(slot => {
        const key = `${slot.date}|${slot.time}`;
        const currentBookings = bookingsMap.get(key) || 0;
        const available = slot.max_capacity - currentBookings;

        return {
          time: slot.time,
          available,
          full: available <= 0,
        };
      });

    return {
      date: day.date,
      slots: daySlots.filter(slot => !slot.full),
    };
  }).filter(day => day.slots.length > 0); // Remove dates that are fully booked

  return new Response(JSON.stringify(availability), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};
