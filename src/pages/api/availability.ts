import type { APIRoute } from 'astro';
import { supabase } from '../../../lib/supabase';


export const GET: APIRoute = async () => {
  try {
    console.log('Availability API called');

    // Step 1: Get open days
    const { data: openDays, error: openError } = await supabase
      .from('open_days')
      .select('date')
      .eq('is_open', true)
      .order('date', { ascending: true });

    console.log('Open days query result:', { openDays, openError });

    if (openError) {
      console.error('Open days error:', openError);
      return new Response(JSON.stringify({ error: openError.message }), { status: 500 });
    }

    if (!openDays || openDays.length === 0) {
      console.log('No open days found');
      return new Response(JSON.stringify({}), { status: 200, headers: { 'Content-Type': 'application/json' } });
    }

  // Step 2: Get time slots for open days
  const { data: slots, error: slotError } = await supabase
    .from('time_slots')
    .select('id, date, time, max_capacity')
    .in('date', openDays.map(d => d.date));

  if (slotError || !slots) {
    return new Response(JSON.stringify({ error: slotError?.message }), { status: 500 });
  }

  // Step 3: Get bookings to count how full each slot is
  const { data: bookings, error: bookingError } = await supabase
    .from('bookings')
    .select('date, time, number_of_visitors');

  if (bookingError || !bookings) {
    return new Response(JSON.stringify({ error: bookingError?.message }), { status: 500 });
  }

  // Step 4: Organize availability - count total visitors per slot
  const bookingsMap = new Map<string, number>();
  for (const b of bookings) {
    const key = `${b.date}|${b.time}`;
    // Handle both number_of_visitors and fallback to 1 if missing
    const visitors = b.number_of_visitors || 1;
    bookingsMap.set(key, (bookingsMap.get(key) || 0) + visitors);
  }

  // Step 5: Convert to frontend expected format: { "2025-06-22": ["9:00 AM", "10:00 AM"] }
  const availability: Record<string, string[]> = {};

  for (const day of openDays) {
    const availableTimes = slots
      .filter(slot => slot.date === day.date)
      .filter(slot => {
        const key = `${slot.date}|${slot.time}`;
        const currentVisitors = bookingsMap.get(key) || 0;
        const available = slot.max_capacity - currentVisitors;
        return available > 0;
      })
      .map(slot => slot.time);

    if (availableTimes.length > 0) {
      availability[day.date] = availableTimes;
    }
  }

  console.log('Final availability result:', availability);

  return new Response(JSON.stringify(availability), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  } catch (error) {
    console.error('Availability API error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
