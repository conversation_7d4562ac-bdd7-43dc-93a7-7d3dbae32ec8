// src/pages/api/bookings.ts
import type { APIRoute } from 'astro';
import { supabase } from '../../../lib/supabase';

export const POST: APIRoute = async ({ request }) => {
  try {
    console.log('Booking API called');
    console.log('Request headers:', Object.fromEntries(request.headers.entries()));
    console.log('Request method:', request.method);

    let body;
    try {
      const rawBody = await request.text();
      console.log('Raw request body:', rawBody);

      if (!rawBody || rawBody.trim() === '') {
        throw new Error('Empty request body');
      }

      body = JSON.parse(rawBody);
      console.log('Parsed request body:', body);
    } catch (parseError) {
      console.error('JSON parsing error:', parseError);
      return new Response(JSON.stringify({
        error: 'Invalid JSON in request body',
        details: parseError instanceof Error ? parseError.message : 'Unknown parsing error'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const {
      fullName,
      email,
      phone,
      visitDate,
      preferredTime,
      numberOfVisitors,
      totalAmount
    } = body;

    console.log('Extracted fields:', { fullName, email, phone, visitDate, preferredTime, numberOfVisitors, totalAmount });

    // Basic server-side validation
    if (!fullName || !email || !phone || !visitDate || !preferredTime || !numberOfVisitors) {
      console.log('Validation failed - missing fields');
      return new Response(JSON.stringify({ error: 'Missing required fields.' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return new Response(JSON.stringify({ error: 'Invalid email format.' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate date is not in the past
    const visitDateObj = new Date(visitDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (visitDateObj < today) {
      return new Response(JSON.stringify({ error: 'Cannot book for past dates.' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate number of visitors
    if (numberOfVisitors < 1 || numberOfVisitors > 20) {
      return new Response(JSON.stringify({ error: 'Number of visitors must be between 1 and 20.' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Use a transaction-like approach to prevent race conditions
    // 1. Fetch the time slot capacity for the selected date/time
    console.log('Checking time slot for:', { visitDate, preferredTime });
    const { data: slot, error: slotError } = await supabase
      .from('time_slots')
      .select('max_capacity')
      .eq('date', visitDate)
      .eq('time', preferredTime)
      .maybeSingle();

    console.log('Time slot query result:', { slot, slotError });

    if (slotError) {
      console.error('Time slot query error:', slotError);
      return new Response(JSON.stringify({ error: `Database error: ${slotError.message}` }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!slot) {
      console.log('No time slot found for:', { visitDate, preferredTime });
      return new Response(JSON.stringify({ error: 'Selected time slot is not available.' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 2. Count existing visitors (not just bookings) for this slot
    console.log('Checking existing bookings for:', { visitDate, preferredTime });
    const { data: existingBookings, error: bookingError } = await supabase
      .from('bookings')
      .select('number_of_visitors')
      .eq('date', visitDate)
      .eq('time', preferredTime);

    console.log('Existing bookings query result:', { existingBookings, bookingError });

    if (bookingError) {
      console.error('Booking query error:', bookingError);
      return new Response(JSON.stringify({ error: `Could not verify availability: ${bookingError.message}` }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const totalVisitorsBooked = existingBookings.reduce((sum, booking) => sum + booking.number_of_visitors, 0);

    // 3. Check if slot still has capacity
    if (totalVisitorsBooked + numberOfVisitors > slot.max_capacity) {
      return new Response(JSON.stringify({ error: 'That time slot has just filled up. Please pick another time.' }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 4. Insert new booking
    console.log('Inserting booking with data:', {
      full_name: fullName,
      email,
      phone,
      date: visitDate,
      time: preferredTime,
      number_of_visitors: numberOfVisitors,
      total_amount: totalAmount,
    });

    const { error: insertError } = await supabase.from('bookings').insert({
      full_name: fullName,
      email,
      phone,
      date: visitDate,
      time: preferredTime,
      number_of_visitors: numberOfVisitors,
      total_amount: totalAmount,
    });

    console.log('Insert result:', { insertError });

    if (insertError) {
      console.error('Insert error:', insertError);
      return new Response(JSON.stringify({ error: `Booking failed: ${insertError.message}` }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Booking confirmed! You will receive a confirmation email shortly.'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Booking API error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error. Please try again.' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
