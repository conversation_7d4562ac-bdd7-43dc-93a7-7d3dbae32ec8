// src/pages/api/bookings.ts
import type { APIRoute } from 'astro';
import { supabase } from '../../../lib/supabase';

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();

    const {
      fullName,
      email,
      phone,
      visitDate,
      preferredTime,
      numberOfVisitors,
      totalAmount
    } = body;

    // Basic server-side validation
    if (!fullName || !email || !phone || !visitDate || !preferredTime || !numberOfVisitors) {
      return new Response(JSON.stringify({ error: 'Missing required fields.' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return new Response(JSON.stringify({ error: 'Invalid email format.' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate date is not in the past
    const visitDateObj = new Date(visitDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (visitDateObj < today) {
      return new Response(JSON.stringify({ error: 'Cannot book for past dates.' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate number of visitors
    if (numberOfVisitors < 1 || numberOfVisitors > 20) {
      return new Response(JSON.stringify({ error: 'Number of visitors must be between 1 and 20.' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Use a transaction-like approach to prevent race conditions
    // 1. Fetch the time slot capacity for the selected date/time
    const { data: slot, error: slotError } = await supabase
      .from('time_slots')
      .select('max_capacity')
      .eq('date', visitDate)
      .eq('time', preferredTime)
      .maybeSingle();

    if (slotError || !slot) {
      return new Response(JSON.stringify({ error: 'Selected time slot is not available.' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 2. Count existing visitors (not just bookings) for this slot
    const { data: existingBookings, error: bookingError } = await supabase
      .from('bookings')
      .select('number_of_visitors')
      .eq('date', visitDate)
      .eq('time', preferredTime);

    if (bookingError) {
      return new Response(JSON.stringify({ error: 'Could not verify availability.' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const totalVisitorsBooked = existingBookings.reduce((sum, booking) => sum + booking.number_of_visitors, 0);

    // 3. Check if slot still has capacity
    if (totalVisitorsBooked + numberOfVisitors > slot.max_capacity) {
      return new Response(JSON.stringify({ error: 'That time slot has just filled up. Please pick another time.' }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 4. Insert new booking
    const { error: insertError } = await supabase.from('bookings').insert({
      full_name: fullName,
      email,
      phone,
      date: visitDate,
      time: preferredTime,
      number_of_visitors: numberOfVisitors,
      total_amount: totalAmount,
    });

    if (insertError) {
      return new Response(JSON.stringify({ error: insertError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Booking confirmed! You will receive a confirmation email shortly.'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Booking API error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error. Please try again.' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
