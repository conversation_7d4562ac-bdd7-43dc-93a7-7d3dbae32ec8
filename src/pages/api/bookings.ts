// src/pages/api/bookings.ts
import type { APIRoute } from 'astro';
import { supabase } from '../../../lib/supabase';

export const POST: APIRoute = async ({ request }) => {
  const body = await request.json();

  const {
    fullName,
    email,
    phone,
    visitDate,
    preferredTime,
    numberOfVisitors,
    totalAmount
  } = body;

  // Basic server-side validation
  if (!fullName || !email || !phone || !visitDate || !preferredTime || !numberOfVisitors) {
    return new Response(JSON.stringify({ error: 'Missing required fields.' }), { status: 400 });
  }

  // 1. Fetch the time slot capacity for the selected date/time
  const { data: slot, error: slotError } = await supabase
    .from('time_slots')
    .select('max_capacity')
    .eq('date', visitDate)
    .eq('time', preferredTime)
    .maybeSingle();

  if (slotError || !slot) {
    return new Response(JSON.stringify({ error: 'Selected time slot is not available.' }), { status: 404 });
  }

  // 2. Count existing bookings for this slot
  const { data: existingBookings, error: bookingError } = await supabase
    .from('bookings')
    .select('id')
    .eq('date', visitDate)
    .eq('time', preferredTime);

  if (bookingError) {
    return new Response(JSON.stringify({ error: 'Could not verify availability.' }), { status: 500 });
  }

  const totalBooked = existingBookings.length;

  // 3. Check if slot still has capacity
  if (totalBooked + numberOfVisitors > slot.max_capacity) {
    return new Response(JSON.stringify({ error: 'That time slot has just filled up. Please pick another time.' }), { status: 409 });
  }

  // 4. Insert new booking
  const { error: insertError } = await supabase.from('bookings').insert({
    full_name: fullName,
    email,
    phone,
    date: visitDate,
    time: preferredTime,
    number_of_visitors: numberOfVisitors,
    total_amount: totalAmount,
  });

  if (insertError) {
    return new Response(JSON.stringify({ error: insertError.message }), { status: 500 });
  }

  return new Response(JSON.stringify({ success: true }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
};
