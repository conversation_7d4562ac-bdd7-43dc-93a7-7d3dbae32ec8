---
import Layout from '../layouts/Layout.astro';
---

<Layout
	title="Beautiful Cut-Your-Own Bouquet Farm in Southern Alberta"
	description="Visit BG Bouquet Garden near Waterton National Park. Create stunning bouquets from our seasonal flower fields. Open daily 9AM-6PM. Book your visit today!"
	image="/og-image.jpg"
>
	<!-- Hero Section -->
	<section id="home" class="min-h-screen flex items-center py-20 texture-background-home">
		<div class="max-w-7xl mx-auto px-4 w-full">
			<div class="grid lg:grid-cols-5 gap-12 items-center">

				<!-- Left Column - Content -->
				<div class="lg:col-span-3 space-y-8 flex flex-col justify-between h-full">

						<h1 class="text-4xl md:text-6xl lg:text-7xl xl:text-[8.5rem] font-allura text-charcoal leading-none hero-header-text center-mobile-text">
							<PERSON><PERSON>, <PERSON>ni<PERSON> <br>& <PERSON>ather
						</h1>
						
						<p class="text-xl md:text-2xl font-roboto text-charcoal leading-relaxed center-mobile-text">
							A U-pick fresh-cut flower bouquet experience for everyone! Perfect for relaxing, having fun, and making joyful memories near the sunny foothills of Southern Alberta.
						</p>

					<!-- Key Information -->
					<div class="shabby-card p-6 distressed-shadow">
						<h3 class="text-xl font-playfair font-bold text-charcoal mb-4 relative center-mobile-text">
							Visit Information
						</h3>
						<div class="grid sm:grid-cols-2 gap-4 text-sm">
							<div class="flex items-top space-x-3">
								<div class="w-10 h-10 rounded-lg flex items-center justify-center vintage-border">
									<svg class="w-5 h-5" style="color: #E8B4B8;" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
									</svg>
								</div>
								<div>
									<p class="font-playfair font-semibold" style="color: #333333;">Location</p>
									<p class="font-roboto" style="color: #333333;">123 Garden Lane, Flower Valley</p>
								</div>
							</div>
							<div class="flex items-top space-x-3">
								<div class="w-10 h-10 rounded-lg flex items-center justify-center vintage-border">
									<svg class="w-5 h-5" style="color: #E8B4B8;" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
									</svg>
								</div>
								<div>
									<p class="font-playfair font-semibold" style="color: #333333;">Hours</p>
									<p class="font-roboto" style="color: #333333;">Daily 9AM-6PM, Closed Mondays</p>
								</div>
							</div>
							<div class="flex items-top space-x-3">
								<div class="w-10 h-10 rounded-lg flex items-center justify-center vintage-border">
									<svg class="w-5 h-5" style="color: #E8B4B8;" fill="currentColor" viewBox="0 0 20 20">
										<path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
									</svg>
								</div>
								<div>
									<p class="font-playfair font-semibold" style="color: #333333;">Contact</p>
									<p class="font-roboto" style="color: #333333;">(*************</p>
								</div>
							</div>
							<div class="flex items-top space-x-3">
								<div class="w-10 h-10 rounded-lg flex items-center justify-center vintage-border">
									<svg class="w-5 h-5" style="color: #E8B4B8;" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M19 7l-.867 12.142A2 2 0 0116.138 21H3.862a2 2 0 01-1.995-1.858L1 7m18 0l-2-4H3l-2 4m18 0H1m11 0V5a1 1 0 00-1-1H9a1 1 0 00-1 1v2h4z" clip-rule="evenodd"></path>
									</svg>
								</div>
								<div>
									<p class="font-playfair font-semibold" style="color: #333333;">Parking</p>
									<p class="font-roboto" style="color: #333333;">Free on-site parking</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Right Column - Booking Form -->
				<div class="lg:col-span-2">
					<div class="shabby-card p-6 lg:p-8 distressed-shadow">


						<div class="text-center mb-4 lg:mb-6">
							<h2 class="text-xl lg:text-2xl font-playfair font-bold text-charcoal mb-1 lg:mb-2">
								Plan Your Visit
							</h2>
							<p class="text-sm lg:text-base font-roboto text-charcoal">Reserve your perfect flower picking experience.</p>
						</div>

						<!-- Vintage Wheelbarrow Illustration Placeholder - Smaller on desktop -->
						<div class="wheel-barrow">
						</div>

						<!-- Booking Form -->
						<form id="booking-form" class="space-y-3">
							<!-- Desktop: 2-column layout, Mobile: single column -->
							<div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
								<div>
									<label for="fullName" class="block text-sm font-playfair font-medium text-charcoal mb-1">Full Name *</label>
									<input
										type="text"
										id="fullName"
										name="fullName"
										required
										class="w-full px-3 py-2.5 shabby-input text-sm font-roboto rounded-sm"
										placeholder="Your full name"
									>
								</div>
								<div>
									<label for="email" class="block text-sm font-playfair font-medium text-charcoal mb-1">Email *</label>
									<input
										type="email"
										id="email"
										name="email"
										required
										class="w-full px-3 py-2.5 shabby-input text-sm font-roboto rounded-sm"
										placeholder="<EMAIL>"
									>
								</div>
								<div>
									<label for="phone" class="block text-sm font-playfair font-medium text-charcoal mb-1">Phone Number *</label>
									<input
										type="tel"
										id="phone"
										name="phone"
										required
										class="w-full px-3 py-2.5 shabby-input text-sm font-roboto rounded-sm"
										placeholder="(*************"
									>
								</div>
								<div>
									<label for="visitDate" class="block text-sm font-playfair font-medium text-charcoal mb-1">Visit Date *</label>
									<input
										type="date"
										id="visitDate"
										name="visitDate"
										required
										class="w-full px-3 py-2.5 shabby-input text-sm font-roboto rounded-sm"
									>
								</div>
								<div>
									<label for="preferredTime" class="block text-sm font-playfair font-medium text-charcoal mb-1">Preferred Time *</label>
									<select
										id="preferredTime"
										name="preferredTime"
										required
										class="w-full px-3 py-2.5 shabby-input text-sm font-roboto rounded-sm"
									>
										<option value="">Select time</option>
										<option value="9:00 AM">9:00 AM</option>
										<option value="10:00 AM">10:00 AM</option>
										<option value="11:00 AM">11:00 AM</option>
										<option value="12:00 PM">12:00 PM</option>
										<option value="1:00 PM">1:00 PM</option>
										<option value="2:00 PM">2:00 PM</option>
										<option value="3:00 PM">3:00 PM</option>
										<option value="4:00 PM">4:00 PM</option>
										<option value="5:00 PM">5:00 PM</option>
									</select>
								</div>
								<div>
									<label for="numberOfVisitors" class="block text-sm font-playfair font-medium text-charcoal mb-1">Bouquets</label>
									<select
										id="numberOfVisitors"
										name="numberOfVisitors"
										class="w-full px-3 py-2.5 shabby-input text-sm font-roboto rounded-sm"
									>
										<option value="1">1 bouquet $35</option>
										<option value="2">2 bouquets $70 </option>
										<option value="3">3 bouquets $105</option>
										<option value="4">4 bouquets $140</option>
										<option value="5">5 bouquets $175</option>
										<option value="6">6 bouquets $210</option>
										<option value="7">7 bouquets $245</option>
										<option value="8">8 bouquets $280</option>
										<option value="9">9 bouquets $315</option>
										<option value="10+">10+ bouquets (call us)</option>
									</select>
								</div>
							</div>

							<!-- Action Buttons - Side by side on desktop, stacked on mobile -->
							<div class="grid grid-cols-1 lg:grid-cols-2 gap-2 pt-2">
								<button
									type="submit"
									id="bookNowBtn"
									class="vintage-button px-6 py-3 disabled:opacity-50 disabled:cursor-not-allowed text-sm lg:text-base floral-accent relative rounded-sm"
								>
									<span id="bookingBtnText">Book My Bouquet!</span>
									<span id="bookingSpinner" class="hidden">
										<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
											<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
											<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
										</svg>
										Processing...
									</span>
								</button>
								<a
									href="#what-to-expect"
									class="border-2 border-charcoal bg-cream px-6 py-3 rounded-sm font-playfair font-semibold hover:bg-shabby-pink transition-all duration-300 text-center text-sm lg:text-base"
								>
									What to Expect
								</a>
							</div>

							<p class="text-xs font-roboto text-charcoal text-center mt-2">
								Secure payment powered by Stripe. Your information is protected.
							</p>
						</form>
					</div>
				</div>
			</div>
		</div>
		<img src="/src/assets/images/bg-bouquet-left-flower.webp" aria-hidden="true" alt="" pointer-events-none class="hero-decorative-flower-left-bottom desktop-decoration" />
		<img src="/src/assets/images/bg-bouquet-right-flower.webp" aria-hidden="true" alt="" pointer-events-none class="hero-decorative-flower-right-bottom desktop-decoration" />
		<img src="/src/assets/images/bg-bouguet-top-flowers.webp" aria-hidden="true" alt="" pointer-events-none class="hero-decorative-flower-left-top desktop-decoration" />
		<img src="/src/assets/images/bg-bouguet-top-flowers.webp" aria-hidden="true" alt="" pointer-events-none class="hero-decorative-flower-left-top-2 desktop-decoration" />
		<img src="/src/assets/images/bg-bouguet-top-flowers.webp" aria-hidden="true" alt="" pointer-events-none class="hero-decorative-flower-right-top-2 desktop-decoration" />
		<img src="/src/assets/images/bg-bouguet-top-flowers.webp" aria-hidden="true" alt="" pointer-events-none class="hero-decorative-flower-right-top desktop-decoration" />
	</section>

	<!-- What to Expect Section -->
	<section id="what-to-expect" class="py-20" style="position: relative;">

		<!--- WTF -->
		<div id="wood-background-container" class="py-20 texture-background-wood" style=" z-index: 1; background-color:#EFF9FA; background-size: cover; background-blend-mode: multiply; height:100%; position: absolute; top: 0; left: 0; width: 100%;">
		</div>
		<!--- WTF -->

		<div class="max-w-7xl mx-auto px-4" style="z-index: 2; position: relative;">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-playfair font-bold mb-6" style="color: #333333;">What to Expect</h2>
				<p class="text-xl font-roboto max-w-3xl mx-auto" style="color: #333333;">
					Your perfect garden experience awaits! Here's everything you need to know for a magical visit.
				</p>
			</div>

			<!-- Main Content Grid -->
			<div class="grid lg:grid-cols-3 gap-12 mb-16">

				<!-- What to Bring -->
				<div class="shabby-card-blue p-8">
					<div class="text-center">
						<h3 class="text-xl font-playfair font-bold mb-4" style="color: #333333;">What to Bring</h3>
						<img src="/src/assets/images/bg-bouquet-cherry-blossoms.webp" aria-hidden="true" alt="" class="what-to-expect-card-decorative-flower" loading="lazy" />
					</div>
					<ul class="space-y-3 font-roboto" style="color: #333333;">
						<li class="center-text-card">
							<div class="flex items-center flex-col">
								<span><strong>Comfortable walking shoes</strong></span><br><span> Ground can be uneven</span>
							</div>
						</li>
						<li class="center-text-card">
							<div class="flex items-center flex-col">
								<span><strong>Weather protection</strong></span><br><span>Hat, sunglasses, light jacket, sunscreen</span>
							</div>
						</li>
						<li class="center-text-card">
							<div class="flex items-center flex-col">
								<span><strong>Water bottle</strong></span><br><span>Stay hydrated during your visit</span>
							</div>
						</li>
						<li class="center-text-card">
							<div class="flex items-center flex-col">
								<span><strong>Camera or phone</strong></span><br><span>Capture beautiful memories</span>
							</div>
						</li>
					</ul>
				</div>

				<!-- What's Included -->
				<div class="shabby-card-blue p-8">
					<div class="text-center">
						<h3 class="text-xl font-playfair font-bold mb-4" style="color: #333333;">What's Included</h3>
						<img src="/src/assets/images/bg-bouquet-yellow-flowers.webp" aria-hidden="true" alt="" class="what-to-expect-card-decorative-flower" loading="lazy" />
					</div>
					<ul class="space-y-3 font-roboto" style="color: #333333;">
						<li>
							<div class="flex items-center flex-col">
								<span><strong>Cutting tools</strong></span><br><span>Shears/Scissors provided</span>
							</div>
						</li>
						<li>
							<div class="flex items-center flex-col">
								<span><strong>Collection vessel</strong></span><br><span>Perfect for gathering your blooms</span>
							</div>
						</li>
						<li>
							<div class="flex items-center flex-col">
								<span><strong>Care instructions</strong></span><br><span>Keep your flowers fresh longer</span>
							</div>
						</li>
						<li>
							<div class="flex items-center flex-col">
								<span><strong>Free parking</strong></span><br><span>Convenient on-site parking</span>
							</div>
						</li>
					</ul>
				</div>

				<!-- Farm Guidelines -->
				<div class="shabby-card-blue p-8">
					<div class="text-center">
						<h3 class="text-xl font-playfair font-bold mb-4" style="color: #333333;">Gentle Guidelines</h3>
						<img src="/src/assets/images/bg-bouquet-bluebells.webp" aria-hidden="true" alt="" class="what-to-expect-card-decorative-flower" loading="lazy" />
					</div>
					<ul class="space-y-3 font-roboto" style="color: #333333;">
						<li>
							<div class="flex items-center flex-col">
								<span><strong>Stay on marked paths</strong></span><br><span>Protects both you and the flowers</span>
							</div>
						</li>
						<li>
							<div class="flex items-center flex-col">
								<span><strong>Cut stems cleanly</strong></span><br><span>Use provided tools for best results</span>
							</div>
						</li>
						<li>
							<div class="flex items-center flex-col">
								<span><strong>Children welcome</strong></span><br><span>Please supervise little ones</span>
							</div>
						</li>
						<li>
							<div class="flex items-center flex-col">
								<span><strong>Pet supervision</strong></span><br><span>Keep in parking area to protect flowers</span>
							</div>
						</li>
					</ul>
				</div>				 
			</div>

			<!-- Experience Highlights -->
			<div class="grid lg:grid-cols-3 gap-12 mb-12">
				<div class="text-center">
					<div class="w-16 h-16 rounded-sm flex items-center justify-center mx-auto mb-4">
					<svg
						viewBox="0 0 457.378 457.378"
						class="w-16 h-16"
						fill="currentColor"
						style="color: #E8B4B8;"
						>
						<g>
							<path d="M78.856,208.152c3.542-1.944,7.191-3.746,11.003-5.337c15.102-6.291,32.065-9.488,50.396-9.488 c10.17,0,20.056,1.014,29.424,2.575l-56.048-37.98l91.612,46.942c6.522,2.214,12.181,4.422,16.745,6.343 c0.016-0.044,0.038-0.105,0.06-0.166c-5.997-23.929-23.275-75.6-64.672-100.116c-20.312-12.024-43.581-15.808-64.83-15.808 c-36.164,0-66.489,10.959-66.489,10.959S36.402,171.537,78.856,208.152z" />
							<path d="M107.582,281.036l60.732-29.775c13.909-13.609,27.675-23.548,36.488-29.274 c-16.236-5.886-39.708-12.37-64.554-12.37c-14.659,0-29.792,2.252-44.128,8.233c-58.871,24.523-82.477,102.309-82.477,102.309 s41.088,21.745,86.575,21.745c7.019,0,14.148-0.556,21.243-1.756c0.344-3.641,0.75-7.288,1.448-10.959 c4.871-25.649,18.194-47.5,33.208-64.981L107.582,281.036z" />
							<path d="M197.97,357.403l15.492-121.606c-19.268,12.587-65.197,47.169-74.558,96.431 c-11.904,62.653,40.084,125.15,40.084,125.15s69.109-38.02,82.499-99.082c-26.198-27.996-36.248-65.85-39.964-93.631 L197.97,357.403z" />
							<path d="M363.337,260.049c-2.117-1.914-4.427-3.475-6.666-5.187c-12.632,3.814-26.293,5.855-40.945,5.855 c-15.403,0-29.995-2.2-42.898-5.052l58.474,60.019l-83.259-66.842c-4.682-1.568-8.879-3.07-12.085-4.347 c1.411,25.694,8.624,77.148,43.011,108.397c30.571,27.772,75.082,32.667,102.661,32.667c15.005,0,24.995-1.45,24.995-1.45 S410.529,302.924,363.337,260.049z" />
							<path d="M351.492,171.612l-94.997,51.507c-4.21,3.258-7.851,5.809-10.688,7.686 c14.45,5.426,40.954,13.631,69.92,13.631c16.536,0,33.776-2.703,49.81-10.171c57.812-26.917,78.198-105.618,78.198-105.618 s-38.273-18.277-81.313-18.277c-16.221,0.008-33.096,2.665-48.873,9.803c-1.367,34.002-15.89,60.191-31.819,78.889L351.492,171.612 z" />
							<path d="M229.579,180.53l4.804-78.77l4.375,71.578c0,0,0.007-0.008,0.007-0.016l-0.24,7.859l-0.675,34.753 C283.832,184.091,344.771,99.134,234.383,0c0,0-53.759,44.398-61.664,101.647C202.384,122.313,219.836,154.618,229.579,180.53z" />
						</g>
					</svg>

					</div>
					<h3 class="text-xl font-playfair font-bold mb-2" style="color: #333333;">Seasonal Blooms</h3>
					<p class="font-roboto text-md" style="color: #333333;">Different flowers bloom throughout the year - spring tulips, summer sunflowers, fall dahlias</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 rounded-sm flex items-center justify-center mx-auto mb-4" >
						<svg
							viewBox="0 0 64 64"
							class="w-16 h-16"
							fill="currentColor"
							style="color: #E8B4B8;"
							>
							<path d="M32,48c6.627,0,12-5.373,12-12s-5.373-12-12-12s-12,5.373-12,12S25.373,48,32,48z M32,28c4.418,0,8,3.582,8,8
								c0,0.553-0.447,1-1,1s-1-0.447-1-1c0-3.313-2.687-6-6-6c-0.553,0-1-0.447-1-1S31.447,28,32,28z"/>
							<path d="M32,52c8.837,0,16-7.162,16-16c0-8.837-7.163-16-16-16s-16,7.163-16,16C16,44.838,23.163,52,32,52z M32,22
								c7.732,0,14,6.268,14,14s-6.268,14-14,14s-14-6.268-14-14S24.268,22,32,22z"/>
							<circle cx="55" cy="21" r="1"/>
							<path d="M60,12c0,0-7,0-8,0s-1.582,0.004-2.793-1.207s-5.538-5.538-5.538-5.538C43.481,5.067,42.33,4,41,4S24.453,4,23,4
								s-2.498,1.084-2.686,1.271c0,0-4.326,4.326-5.521,5.521S13.018,12,12,12V9c0-0.553-0.447-1-1-1H5C4.447,8,4,8.447,4,9v3
								c-2.211,0-4,1.789-4,4v12h15.893C18.84,22.078,24.937,18,32,18s13.16,4.078,16.107,10H64V16C64,13.789,62.211,12,60,12z
								M10,12c-1.24,0-2.782,0-4,0v-2h4V12z M55,24c-1.657,0-3-1.344-3-3s1.343-3,3-3s3,1.344,3,3S56.657,24,55,24z"/>
							<path d="M50,36c0,9.941-8.059,18-18,18s-18-8.059-18-18c0-2.107,0.381-4.121,1.046-6H0v26c0,2.211,1.789,4,4,4h56
								c2.211,0,4-1.789,4-4V30H48.954C49.619,31.879,50,33.893,50,36z"/>
						</svg>

					</div>
					<h3 class="text-xl font-playfair font-bold mb-2" style="color: #333333;">Perfect Photos</h3>
					<p class="font-roboto text-md" style="color: #333333;">Instagram-worthy backdrop - The Rocky Mountains offer a magnificent view</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 rounded-sm flex items-center justify-center mx-auto mb-4">
						<svg

							viewBox="0 0 64 64"
							class="w-16 h-16"
							fill="currentColor"
							style="color: #E8B4B8;"
							>
							<path d="M36.169,34.754c-0.372,0.562-0.854,1.043-1.415,1.415l7.853,7.853c0.391,0.391,1.023,0.391,1.414-0.001
								c0.391-0.391,0.391-1.023,0-1.414L36.169,34.754z"/>
							<path d="M33,27.101V14c0-0.553-0.447-1-1-1s-1,0.447-1,1v13.101C31.323,27.035,31.657,27,32,27S32.677,27.035,33,27.101z"/>
							<path d="M32,29c-1.657,0-3,1.343-3,3s1.343,3,3,3s3-1.343,3-3S33.657,29,32,29z M32,33c-0.553,0-1-0.447-1-1s0.447-1,1-1
								s1,0.447,1,1S32.553,33,32,33z"/>
							<path d="M54,32c0-0.553,0.447-1,1-1h2.975c-0.243-6.425-2.815-12.252-6.899-16.661l-2.105,2.104
								c-0.391,0.391-1.023,0.391-1.414,0s-0.391-1.023,0-1.414l2.104-2.104C45.251,8.84,39.424,6.269,32.999,6.025V9
								c0,0.553-0.447,1-1,1s-1-0.447-1-1V6.025c-6.425,0.243-12.252,2.815-16.661,6.9l2.104,2.104c0.391,0.391,0.391,1.022,0,1.414
								c-0.391,0.391-1.023,0.391-1.414,0l-2.104-2.104C8.84,18.75,6.269,24.577,6.025,31.002h2.974c0.553,0,0.999,0.446,1,1
								c0,0.552-0.447,0.999-1,1H6.025c0.244,6.425,2.816,12.251,6.9,16.66l2.104-2.104c0.391-0.391,1.021-0.391,1.414,0
								c0.39,0.39,0.39,1.022,0,1.414l-2.104,2.104c4.409,4.084,10.235,6.655,16.66,6.898l0.001-2.974c0-0.553,0.446-0.999,1-1
								c0.551,0,0.998,0.447,1,1l-0.001,2.974c6.425-0.243,12.251-2.815,16.66-6.9l-2.104-2.104c-0.391-0.392-0.391-1.023,0-1.415
								c0.39-0.39,1.022-0.39,1.415,0l2.103,2.104c4.085-4.409,6.656-10.235,6.899-16.66H55C54.447,33,54,32.553,54,32z
								M45.435,45.435c-1.172,1.172-3.07,1.173-4.242,0.001l-8.505-8.505C32.461,36.962,32.235,37,32,37c-2.762,0-5-2.238-5-5
								c0-1.631,0.792-3.064,2-3.978V14c0-1.657,1.343-3,3-3s3,1.343,3,3v14.022c1.208,0.913,2,2.347,2,3.978
								c0,0.236-0.038,0.461-0.069,0.688l8.504,8.504C46.606,42.364,46.606,44.263,45.435,45.435z"/>
							<path d="M32,0C14.327,0,0,14.327,0,32s14.327,32,32,32s32-14.327,32-32S49.673,0,32,0z M32,60
								C16.536,60,4,47.464,4,32S16.536,4,32,4s28,12.536,28,28S47.464,60,32,60z"/>
						</svg>
					</div>
					<h3 class="text-xl font-playfair font-bold mb-2" style="color: #333333;">Take Your Time</h3>
					<p class="font-roboto text-md" style="color: #333333;">No rush - take your time enjoying the peaceful garden atmosphere</p>
				</div>
			</div>

			<!-- Call to Action -->
			<div class="text-center mt-12">
				<p class="font-roboto text-lg mb-6" style="color: #333333;">Ready to create your perfect bouquet?</p>
				<a href="#home" class="vintage-button px-8 py-4 text-lg font-playfair font-semibold inline-block rounded-sm">
					Book My Bouquet Experience!
				</a>
			</div>
		</div>

	</section>

	<!-- Flowers Section -->
	<section id="flowers" class="py-20" style="position: relative;">
		<div class="max-w-7xl mx-auto px-4" style="z-index:2; position: relative;">
			<!-- Header -->
			<div class="text-center mb-12">
				<h2 class="text-4xl md:text-5xl font-playfair font-bold mb-6" style="color: #333333;">Our Beautiful Flowers</h2>
				<p class="text-xl font-roboto max-w-3xl mx-auto" style="color: #333333;">
					Discover our carefully curated collection of seasonal blooms, perfect for creating your dream bouquet.
				</p>
			</div>

			<!-- Search and Controls -->
			<div class="mb-8">
				<!-- Search Bar -->
				<div class="max-w-2xl mx-auto mb-6">
					<div class="relative">
						<label for="flowerSearch" class="sr-only">Search for flowers</label>
						<input
							type="text"
							id="flowerSearch"
							name="flowerSearch"
							placeholder="Search for flowers (e.g., roses, tulips, sunflowers...)"
							class="w-full px-4 py-3 pl-12 pr-4 shabby-input font-roboto text-lg"
							autocomplete="off"
							aria-label="Search for flowers"
							aria-describedby="search-help"
						>
						<svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5" style="color: #E8B4B8;" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
						</svg>
						<div id="searchClear" class="absolute right-4 top-1/2 transform -translate-y-1/2 cursor-pointer hidden">
							<svg class="w-5 h-5" style="color: #E8B4B8;" fill="currentColor" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
							</svg>
						</div>

						<!-- Autocomplete Suggestions -->
						<div id="searchSuggestions" class="absolute top-full left-0 right-0 bg-white rounded-lg shadow-lg border-2 hidden z-10 max-h-60 overflow-y-auto" style="border-color: #E8B4B8;">
							<!-- Suggestions will be populated by JavaScript -->
						</div>
					</div>
				</div>

				<!-- Filter Controls -->
				<div class="flex flex-wrap justify-center gap-4 mb-6">
					<!-- View Toggle (Mobile/Desktop) -->
					<div class="flex items-center space-x-2 lg:hidden">
						<button id="carouselView" class="px-4 py-2 rounded-lg font-playfair font-medium transition-all duration-200 vintage-button text-sm">
							Carousel
						</button>
						<button id="listView" class="px-4 py-2 rounded-lg font-playfair font-medium transition-all duration-200 border-2 text-sm" style="border-color: #E8B4B8; color: #E8B4B8; background-color: #FAF7F4;">
							List
						</button>
					</div>
					<div>
					<!-- Filters -->
					<div>
						<label for="colorFilter" class="sr-only">Filter by color</label>
						<select id="colorFilter" name="colorFilter" class="px-4 py-2 rounded-lg shabby-input font-roboto text-sm" aria-label="Filter flowers by color">
							<option value="">All Colors</option>
							<option value="red">Red</option>
							<option value="pink">Pink</option>
							<option value="white">White</option>
							<option value="yellow">Yellow</option>
							<option value="purple">Purple</option>
							<option value="orange">Orange</option>
							<option value="blue">Blue</option>
						</select>
					</div>

					<div>
						<label for="occasionFilter" class="sr-only">Filter by occasion</label>
						<select id="occasionFilter" name="occasionFilter" class="px-4 py-2 rounded-lg shabby-input font-roboto text-sm" aria-label="Filter flowers by occasion">
							<option value="">All Occasions</option>
							<option value="wedding">Wedding</option>
							<option value="birthday">Birthday</option>
							<option value="anniversary">Anniversary</option>
							<option value="sympathy">Sympathy</option>
							<option value="everyday">Everyday</option>
						</select>
					</div>
					</div>

				</div>
			</div>

			<!-- Mobile Carousel View -->
			<div id="mobileCarousel" class="lg:hidden">
				<div class="relative">
					<!-- Carousel Container -->
					<div class="overflow-x-auto scrollbar-hide touch-scroll px-4" style="scroll-snap-type: x mandatory; scroll-behavior: smooth;">
						<div id="carouselTrack" class="flex gap-4 pb-2" role="region" aria-label="Flower carousel" tabindex="0">
							<!-- Flower cards will be inserted here by JavaScript -->
						</div>
					</div>

					<!-- Carousel Controls -->
					<div class="flex justify-between items-center mt-6" role="group" aria-label="Carousel navigation">
						<button id="prevBtn" class="p-3 rounded-lg vintage-border min-w-12 min-h-12" style="background-color: #FAF7F4; color: #E8B4B8;" disabled aria-label="Previous flower">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
								<path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
							</svg>
						</button>

						<!-- Flower Navigation Dropdown -->
						<div class="relative">
							<button id="flowerNavBtn" class="px-4 py-2 rounded-lg vintage-border font-roboto text-sm min-h-12 flex items-center space-x-2" style="background-color: #FAF7F4; color: #333333; border-color: #E8B4B8;" aria-label="Select flower to view">
								<span id="flowerNavText">1 of 15</span>
								<svg class="w-4 h-4" style="color: #E8B4B8;" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
									<path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
								</svg>
							</button>

							<!-- Dropdown Menu -->
							<div id="flowerNavDropdown" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 max-h-60 overflow-y-auto bg-white rounded-lg shadow-lg border-2 hidden z-20" style="border-color: #E8B4B8;">
								<div class="p-2">
									<div id="flowerNavList" class="space-y-1">
										<!-- Flower list will be populated by JavaScript -->
									</div>
								</div>
							</div>
						</div>

						<button id="nextBtn" class="p-3 rounded-lg vintage-border min-w-12 min-h-12" style="background-color: #FAF7F4; color: #E8B4B8;" aria-label="Next flower">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
								<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
							</svg>
						</button>
					</div>
				</div>
			</div>

			<!-- Mobile List View -->
			<div id="mobileList" class="lg:hidden hidden">
				<div id="listContainer" class="space-y-4">
					<!-- List items will be inserted here by JavaScript -->
				</div>
			</div>

			<!-- Desktop Grid View -->
			<div id="desktopGrid" class="hidden lg:grid lg:grid-cols-4 xl:grid-cols-4 gap-6">
				<!-- Grid items will be inserted here by JavaScript -->
			</div>

			<!-- No Results / Request Form -->
			<div id="noResults" class="hidden text-center py-12">
				<div class="max-w-2xl mx-auto">
					<div class="mb-8">
						<svg class="w-16 h-16 mx-auto mb-4" style="color: #E8B4B8;" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
						</svg>
						<h3 class="text-2xl font-playfair font-bold mb-4" style="color: #333333;">No flowers found matching your search.</h3>
						<p class="font-roboto mb-6" style="color: #333333;">
							We may still be able to help. Request your flower below!
						</p>

						<!-- Reset Search Button -->
						<button id="resetSearchBtn" class="mb-8 px-6 py-3 border-2 rounded-lg font-playfair font-semibold transition-all duration-200 hover:bg-shabby-pink" style="border-color: #E8B4B8; color: #E8B4B8; background-color: #FAF7F4;">
							Reset Search
						</button>
					</div>

					<!-- Flower Request Form -->
					<div class="shabby-card p-6 distressed-shadow">
						<h4 class="text-xl font-playfair font-bold mb-4" style="color: #333333;">Request a Flower</h4>
						<form id="flowerRequestForm" class="space-y-4" aria-label="Request a flower">
							<div class="grid md:grid-cols-2 gap-4">
								<div>
									<label for="requestedFlower" class="sr-only">Flower name</label>
									<input
										type="text"
										id="requestedFlower"
										name="requestedFlower"
										placeholder="Flower name"
										class="shabby-input px-3 py-2 font-roboto"
										required
										aria-describedby="flower-help"
									>
								</div>
								<div>
									<label for="requesterEmail" class="sr-only">Your email</label>
									<input
										type="email"
										id="requesterEmail"
										name="requesterEmail"
										placeholder="Your email"
										class="shabby-input px-3 py-2 font-roboto"
										required
										aria-describedby="email-help"
									>
								</div>
							</div>
							<div>
								<label for="requestNotes" class="sr-only">Additional notes</label>
								<textarea
									id="requestNotes"
									name="requestNotes"
									placeholder="Tell us more about what you're looking for..."
									rows="3"
									class="w-full shabby-input px-3 py-2 font-roboto"
									aria-describedby="notes-help"
								></textarea>
							</div>
							<div id="flower-help" class="sr-only">Enter the name of the flower you're looking for</div>
							<div id="email-help" class="sr-only">We'll contact you about availability</div>
							<div id="notes-help" class="sr-only">Optional additional details about your flower request</div>
							<div class="flex flex-col sm:flex-row gap-3 justify-center">
								<button type="submit" class="vintage-button px-6 py-3 font-playfair font-semibold">
									Send Request
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>


		</div>
		<!--- WTF -->
		<div id="pink-background-container" class="py-20" style=" z-index: 1; background-color:#F8E7E8; background-size: cover; background-blend-mode: multiply; height:100%; position: absolute; top: 0; left: 0; width: 100%;">
		</div>
		<!--- WTF -->
	</section>

	<!-- About Section -->
	<section id="about" class="py-20 bg-white" style="position: relative;">
		<div class="max-w-6xl mx-auto px-4" style="z-index: 2; position: relative;">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold font-playfair text-gray-900 mb-6">About Our Garden</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Our bouquet garden is a carefully curated collection of the most beautiful flowers,
					designed to inspire and delight visitors of all ages.
				</p>
			</div>
			<div class="grid md:grid-cols-2 gap-12 items-center center-mobile-text">
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4 center-mobile-text">Our Story</h3>
					<p class="text-gray-600 mb-6">
BG Bouquets is a welcoming, cut-your-own bouquet farm tucked in the sunny foothills of Southern Alberta — minutes from Hill Spring, Cardston, and Waterton Lakes National Park. Whether you're local or just passing through, our flower field is a place to slow down, breathe deep, and build something beautiful with your own hands.
					</p>
					<p class="text-gray-600">
We're proud to share the simple joys of our family farm in full bloom. Pick from a wide variety of seasonal flowers — including sunflowers, sweet peas, snapdragons, lavender, roses, and more — all grown with care in our prairie soil and perfect for creating your own custom bouquet.
					</p>
				</div>
				<div class="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
					<span class="text-gray-500">Garden Image Placeholder</span>
				</div>
			</div>
		</div>
		<!--- WTF -->
		<div id="white-background-container" class="py-20" style=" z-index: 1; background-color:#fff; background-size: cover; background-blend-mode: multiply; height:100%; position: absolute; top: 0; left: 0; width: 100%;">
		</div>
		<!--- WTF -->		
	</section>

	<!-- Location Section -->
	<section id="location" class="py-20 bg-white">
		<div class="max-w-6xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold font-playfair text-gray-900 mb-6">Visit Us</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Find us in the heart of the southern alberta countryside, on the way to waterton national park.
				</p>
			</div>
			<div class="grid md:grid-cols-2 gap-12">
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-6">Location Details</h3>
					<div class="space-y-4">
						<div class="flex items-start space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<span class="text-white text-xs">📍</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Address</p>
								<p class="text-gray-600">123 Garden Lane, Flower Valley, State 12345</p>
							</div>
						</div>
						<div class="flex items-start space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<span class="text-white text-xs">🕒</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Hours</p>
								<p class="text-gray-600">Daily: 9:00 AM - 6:00 PM</p>
								<p class="text-gray-600">Closed Mondays (except holidays)</p>
							</div>
						</div>
						<div class="flex items-start space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<span class="text-white text-xs">🚗</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Parking</p>
								<p class="text-gray-600">Free parking available on-site</p>
							</div>
						</div>
					</div>
				</div>
				<div class="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
					<span class="text-gray-500">Map Placeholder</span>
				</div>
			</div>
		</div>
	</section>

	<!-- Contact Section -->
	<section id="contact" class="py-20 bg-white">
		<div class="max-w-4xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold font-playfair text-gray-900 mb-6">Get in Touch</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Have questions or want to plan a special event? We'd love to hear from you.
				</p>
			</div>
			<div class="grid md:grid-cols-2 gap-12">
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-6">Contact Information</h3>
					<div class="space-y-4">
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
								<span class="text-white text-xs">📞</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Phone</p>
								<p class="text-gray-600">(*************</p>
							</div>
						</div>
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
								<span class="text-white text-xs">✉️</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Email</p>
								<p class="text-gray-600"><EMAIL></p>
							</div>
						</div>
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
								<span class="text-white text-xs">💬</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Social Media</p>
								<p class="text-gray-600">Follow us @bouquetgarden</p>
							</div>
						</div>
					</div>
				</div>
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h3>
					<form class="space-y-4">
						<div>
							<label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
							<input type="text" id="name" name="name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
						</div>
						<div>
							<label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
							<input type="email" id="email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
						</div>
						<div>
							<label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
							<textarea id="message" name="message" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"></textarea>
						</div>
						<button type="submit" class="w-full bg-green-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-green-700 transition-colors duration-200">
							Send Message
						</button>
					</form>
				</div>
			</div>
		</div>
	</section>
</Layout>

<script>
	// Booking form functionality
	document.addEventListener('DOMContentLoaded', () => {
		const bookingForm = document.getElementById('booking-form') as HTMLFormElement;
		const bookNowBtn = document.getElementById('bookNowBtn') as HTMLButtonElement;
		const bookingBtnText = document.getElementById('bookingBtnText') as HTMLElement;
		const bookingSpinner = document.getElementById('bookingSpinner') as HTMLElement;
		const visitDateInput = document.getElementById('visitDate') as HTMLInputElement;
		const preferredTimeSelect = document.getElementById('preferredTime') as HTMLSelectElement;
		const visitorsSelect = document.getElementById('numberOfVisitors') as HTMLSelectElement;

		const PRICE_PER_BOUQUET = 35;

		// Fetch availability from API
		async function loadAvailability() {
			const response = await fetch('/api/availability');
			const availability = await response.json(); // { "2025-06-22": ["9:00 AM", "10:00 AM", ...], ... }

			// Clear & repopulate visitDateInput options
			const validDates = Object.keys(availability);
			visitDateInput.min = validDates[0] ?? new Date().toISOString().split('T')[0];
			visitDateInput.max = validDates.at(-1) ?? '';

			visitDateInput.addEventListener('change', (e) => {
				const selected = (e.target as HTMLInputElement).value;
				populateTimes(availability[selected] || []);
			});

			// Auto-populate time for the first valid day
			const today = new Date().toISOString().split('T')[0];
			const firstValid = validDates.find(d => d >= today);
			if (firstValid) {
				visitDateInput.value = firstValid;
				populateTimes(availability[firstValid] || []);
			}
		}

		function populateTimes(times: string[]) {
			preferredTimeSelect.innerHTML = '<option value="">Select time</option>';
			times.forEach(time => {
				const opt = document.createElement('option');
				opt.value = time;
				opt.textContent = time;
				preferredTimeSelect.appendChild(opt);
			});
		}

		function updateTotalPrice() {
			const count = parseInt(visitorsSelect.value) || 1;
			const total = count * PRICE_PER_BOUQUET;
			// Optional: update a visible total price element
			// document.getElementById('totalPrice')!.textContent = `$${total}`;
			return total;
		}

		visitorsSelect?.addEventListener('change', updateTotalPrice);

		// Form validation
		function validateForm(): boolean {
			let valid = true;
			['fullName', 'email', 'phone', 'visitDate', 'preferredTime'].forEach(id => {
				const el = document.getElementById(id) as HTMLInputElement | HTMLSelectElement;
				if (!el.value.trim()) {
					el.classList.add('border-red-500');
					valid = false;
				} else {
					el.classList.remove('border-red-500');
				}
			});
			return valid;
		}

		// Phone formatting
		document.getElementById('phone')?.addEventListener('input', (e) => {
			const input = e.target as HTMLInputElement;
			let v = input.value.replace(/\D/g, '');
			if (v.length > 6) v = v.replace(/(\d{3})(\d{3})(\d+)/, '($1) $2-$3');
			else if (v.length > 3) v = v.replace(/(\d{3})(\d+)/, '($1) $2');
			input.value = v;
		});

		// Form submission
		bookingForm.addEventListener('submit', async (e) => {
			e.preventDefault();
			if (!validateForm()) return alert('Please complete all required fields.');

			const formData = new FormData(bookingForm);
			const numberOfVisitors = parseInt(formData.get('numberOfVisitors') as string) || 1;

			const booking = {
				fullName: formData.get('fullName'),
				email: formData.get('email'),
				phone: formData.get('phone'),
				visitDate: formData.get('visitDate'),
				preferredTime: formData.get('preferredTime'),
				numberOfVisitors,
				totalAmount: numberOfVisitors * PRICE_PER_BOUQUET
			};

			// Loading state
			bookNowBtn.disabled = true;
			bookingBtnText.classList.add('hidden');
			bookingSpinner.classList.remove('hidden');

			try {
				const res = await fetch('/api/bookings', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(booking)
				});

				const result = await res.json();

				if (!res.ok) {
					// Show specific error message from server
					alert(result.error || 'There was an error with your booking. Please try again.');
					return;
				}

				// Success
				alert(result.message || 'Booking successful! You’ll receive a confirmation email.');
				bookingForm.reset();
				updateTotalPrice();
				await loadAvailability(); // Refresh times if needed

			} catch (err) {
				console.error('Booking error:', err);
				alert('There was a network error. Please check your connection and try again.');
			} finally {
				bookNowBtn.disabled = false;
				bookingBtnText.classList.remove('hidden');
				bookingSpinner.classList.add('hidden');
			}
		});

		// Load available slots initially
		loadAvailability();
	});

	// Initialize flower section
	initializeFlowerSection();

	// Flower Section Implementation
	function initializeFlowerSection() {
		// Flower data
		const flowers = [
			{
				id: 1,
				name: "Aster",
				description: "Star-shaped blooms in purple, pink, and white that add texture to arrangements",
				colors: ["purple", "pink", "white"],
				occasions: ["wedding", "birthday", "everyday"],
				image: "aster_thumbnail.webp"
			},
			{
				id: 2,
				name: "Baby's Breath",
				description: "Delicate white clouds that create airy, romantic arrangements",
				colors: ["white"],
				occasions: ["wedding", "anniversary", "everyday"],
				image: "babys-breath_thumbnail.webp"
			},
			{
				id: 3,
				name: "Bachelor Buttons",
				description: "Charming cornflower blooms in brilliant blue and mixed colors",
				colors: ["blue", "pink", "white", "purple"],
				occasions: ["wedding", "birthday", "everyday"],
				image: "bachelor-buttons_thumbnail.webp"
			},
			{
				id: 4,
				name: "Daisies",
				description: "Classic white petals with sunny yellow centers, perfect for cheerful bouquets",
				colors: ["white", "yellow"],
				occasions: ["birthday", "everyday"],
				image: "daisies_thumbnail.webp"
			},
			{
				id: 5,
				name: "Eucalyptus",
				description: "Silvery-green foliage that adds fragrance and texture to any arrangement",
				colors: ["green"],
				occasions: ["wedding", "anniversary", "everyday"],
				image: "eucalyptus_thumbnail.webp"
			},
			{
				id: 6,
				name: "Foxgloves",
				description: "Tall spikes of tubular blooms in purple, pink, and white",
				colors: ["purple", "pink", "white"],
				occasions: ["wedding", "everyday"],
				image: "foxgloves_thumbnail.webp"
			},
			{
				id: 7,
				name: "Lavender",
				description: "Aromatic purple spikes perfect for rustic, fragrant arrangements",
				colors: ["purple"],
				occasions: ["wedding", "everyday"],
				image: "lavender_thumbnail.webp"
			},
			{
				id: 8,
				name: "Lupin",
				description: "Dramatic tall spikes in vibrant blues, purples, and mixed colors",
				colors: ["blue", "purple", "pink", "white"],
				occasions: ["wedding", "birthday", "everyday"],
				image: "lupin_thumbnail.webp"
			},
			{
				id: 9,
				name: "Marigolds",
				description: "Bright, cheerful blooms in warm oranges and yellows",
				colors: ["orange", "yellow"],
				occasions: ["birthday", "everyday"],
				image: "marigolds_thumbnail.webp"
			},
			{
				id: 10,
				name: "Peonies",
				description: "Luxurious, full-bodied blooms with incredible fragrance",
				colors: ["pink", "white", "red"],
				occasions: ["wedding", "anniversary"],
				image: "peonies_thumbnail.webp"
			},
			{
				id: 11,
				name: "Roses",
				description: "Classic and hybrid roses in various colors and fragrances",
				colors: ["red", "pink", "white", "yellow"],
				occasions: ["wedding", "anniversary", "everyday"],
				image: "roses_thumbnail.webp"
			},
			{
				id: 12,
				name: "Snapdragons",
				description: "Tall spikes of colorful blooms that add height and drama",
				colors: ["red", "pink", "white", "yellow", "purple"],
				occasions: ["wedding", "birthday", "everyday"],
				image: "snapdragons_thumbnail.webp"
			},
			{
				id: 13,
				name: "Sunflowers",
				description: "Bright, cheerful sunflowers that bring joy to any bouquet",
				colors: ["yellow", "orange"],
				occasions: ["birthday", "everyday"],
				image: "sunflower_thumbnail.webp"
			},
			{
				id: 14,
				name: "Sweet Peas",
				description: "Delicate, fragrant climbers in soft pastel shades",
				colors: ["pink", "purple", "white"],
				occasions: ["wedding", "birthday"],
				image: "sweatpea_thumbnail.webp"
			},
			{
				id: 15,
				name: "Zinnia",
				description: "Vibrant, long-lasting blooms in every color imaginable",
				colors: ["red", "orange", "yellow", "pink", "purple"],
				occasions: ["birthday", "everyday"],
				image: "zinnia_thumbnail.webp"
			}
		];

		let filteredFlowers = [...flowers];
		let currentCardIndex = 0;
		let currentView = 'carousel';

		// Store original classes for restoration
		let originalClasses = {
			mobileCarousel: '',
			mobileList: '',
			desktopGrid: ''
		};

		// Create autocomplete suggestions from flower names
		const commonTypos = {
			'rose': ['roses', 'ros', 'roze'],
			'sunflower': ['sunflowers', 'sun flower', 'sunflowr'],
			'lavender': ['lavendar', 'lavander', 'lavnder'],
			'peony': ['peonies', 'peonie', 'piony'],
			'zinnia': ['zinia', 'zinea', 'zinnea'],
			'marigold': ['marigolds', 'marigol', 'merigold'],
			'snapdragon': ['snapdragons', 'snap dragon', 'snapdragn'],
			'sweet pea': ['sweet peas', 'sweetpea', 'sweetpeas'],
			'baby\'s breath': ['babys breath', 'babies breath', 'baby breath'],
			'bachelor button': ['bachelor buttons', 'batchelor button', 'bachelor botton']
		};

		// DOM elements
		const searchInput = document.getElementById('flowerSearch') as HTMLInputElement;
		const searchClear = document.getElementById('searchClear') as HTMLElement;
		const searchSuggestions = document.getElementById('searchSuggestions') as HTMLElement;
		const colorFilter = document.getElementById('colorFilter') as HTMLSelectElement;
		const occasionFilter = document.getElementById('occasionFilter') as HTMLSelectElement;
		const carouselView = document.getElementById('carouselView') as HTMLButtonElement;
		const listView = document.getElementById('listView') as HTMLButtonElement;
		const mobileCarousel = document.getElementById('mobileCarousel') as HTMLElement;
		const mobileList = document.getElementById('mobileList') as HTMLElement;
		const desktopGrid = document.getElementById('desktopGrid') as HTMLElement;
		const carouselTrack = document.getElementById('carouselTrack') as HTMLElement;
		const listContainer = document.getElementById('listContainer') as HTMLElement;
		const prevBtn = document.getElementById('prevBtn') as HTMLButtonElement;
		const nextBtn = document.getElementById('nextBtn') as HTMLButtonElement;
		const flowerNavBtn = document.getElementById('flowerNavBtn') as HTMLButtonElement;
		const flowerNavText = document.getElementById('flowerNavText') as HTMLElement;
		const flowerNavDropdown = document.getElementById('flowerNavDropdown') as HTMLElement;
		const flowerNavList = document.getElementById('flowerNavList') as HTMLElement;
		const noResults = document.getElementById('noResults') as HTMLElement;
		const resetSearchBtn = document.getElementById('resetSearchBtn') as HTMLButtonElement;
		const flowerRequestForm = document.getElementById('flowerRequestForm') as HTMLFormElement;

		// Initialize
		storeOriginalClasses();
		updateDisplay();
		setupEventListeners();

		function storeOriginalClasses() {
			// Store the original classes for later restoration
			if (mobileCarousel) {
				originalClasses.mobileCarousel = mobileCarousel.className;
			}
			if (mobileList) {
				originalClasses.mobileList = mobileList.className;
			}
			if (desktopGrid) {
				originalClasses.desktopGrid = desktopGrid.className;
			}
		}

		function setupEventListeners() {
			// Search functionality
			searchInput?.addEventListener('input', debounce(handleSearch, 300));
			searchInput?.addEventListener('focus', showSuggestions);
			searchInput?.addEventListener('blur', debounce(hideSuggestions, 200));
			searchClear?.addEventListener('click', clearSearch);

			// Filter functionality
			colorFilter?.addEventListener('change', applyFilters);
			occasionFilter?.addEventListener('change', applyFilters);

			// View toggle (mobile only)
			carouselView?.addEventListener('click', () => switchView('carousel'));
			listView?.addEventListener('click', () => switchView('list'));

			// Carousel controls
			prevBtn?.addEventListener('click', () => navigateCarousel(-1));
			nextBtn?.addEventListener('click', () => navigateCarousel(1));

			// Reset search button
			resetSearchBtn?.addEventListener('click', resetSearch);

			// Flower navigation dropdown
			flowerNavBtn?.addEventListener('click', toggleFlowerNavDropdown);

			// Keyboard support for dropdown
			flowerNavBtn?.addEventListener('keydown', (e: any) => {
				if (e.key === 'Enter' || e.key === ' ') {
					e.preventDefault();
					toggleFlowerNavDropdown();
				} else if (e.key === 'Escape') {
					hideFlowerNavDropdown();
				}
			});

			// Close dropdown when clicking outside
			document.addEventListener('click', (e) => {
				if (!flowerNavBtn?.contains(e.target as Node) && !flowerNavDropdown?.contains(e.target as Node)) {
					hideFlowerNavDropdown();
				}
			});

			// Enhanced touch/swipe support for carousel
			let startX = 0;
			let currentX = 0;
			let isDragging = false;
			let startTime = 0;

			const carouselContainer = mobileCarousel?.querySelector('.overflow-x-auto');

			carouselContainer?.addEventListener('touchstart', (e: any) => {
				startX = e.touches[0].clientX;
				startTime = Date.now();
				isDragging = true;
			}, { passive: true });

			carouselContainer?.addEventListener('touchmove', (e: any) => {
				if (!isDragging) return;
				currentX = e.touches[0].clientX;
			}, { passive: true });

			carouselContainer?.addEventListener('touchend', () => {
				if (!isDragging) return;
				const diff = startX - currentX;
				const timeDiff = Date.now() - startTime;
				const velocity = Math.abs(diff) / timeDiff;

				// Lower threshold for faster swipes, higher for slower ones
				const threshold = velocity > 0.5 ? 30 : 80;

				if (Math.abs(diff) > threshold) {
					if (diff > 0) {
						navigateCarousel(1); // Swipe left = next flower
					} else {
						navigateCarousel(-1); // Swipe right = previous flower
					}
				}
				isDragging = false;
			}, { passive: true });

			// Keyboard navigation support
			carouselContainer?.addEventListener('keydown', (e: any) => {
				if (e.key === 'ArrowLeft') {
					e.preventDefault();
					navigateCarousel(-1);
				} else if (e.key === 'ArrowRight') {
					e.preventDefault();
					navigateCarousel(1);
				}
			});

			// Flower request form
			flowerRequestForm?.addEventListener('submit', handleFlowerRequest);
		}

		// Utility functions
		function debounce(func: Function, wait: number) {
			let timeout: NodeJS.Timeout;
			return function executedFunction(...args: any[]) {
				const later = () => {
					clearTimeout(timeout);
					func(...args);
				};
				clearTimeout(timeout);
				timeout = setTimeout(later, wait);
			};
		}

		function handleSearch() {
			const query = searchInput?.value.toLowerCase() || '';

			if (query.length > 0) {
				searchClear?.classList.remove('hidden');
			} else {
				searchClear?.classList.add('hidden');
			}

			applyFilters();
		}

		function clearSearch() {
			if (searchInput) {
				searchInput.value = '';
			}
			searchClear?.classList.add('hidden');
			applyFilters();
		}

		function applyFilters() {
			const query = searchInput?.value.toLowerCase() || '';
			const color = colorFilter?.value || '';
			const occasion = occasionFilter?.value || '';

			if (query.length > 0) {
				// Search mode: filter by flower name only
				filteredFlowers = flowers.filter(flower => {
					return flower.name.toLowerCase().includes(query);
				});

				// If we have name matches, apply other filters
				if (filteredFlowers.length > 0) {
					filteredFlowers = filteredFlowers.filter(flower => {
						const matchesColor = !color || flower.colors.includes(color);
						const matchesOccasion = !occasion || flower.occasions.includes(occasion);
						return matchesColor && matchesOccasion;
					});
				}
			} else {
				// No search query: show all flowers with filters applied
				filteredFlowers = flowers.filter(flower => {
					const matchesColor = !color || flower.colors.includes(color);
					const matchesOccasion = !occasion || flower.occasions.includes(occasion);
					return matchesColor && matchesOccasion;
				});
			}

			// Always sort alphabetically (default behavior)
			filteredFlowers.sort((a, b) => a.name.localeCompare(b.name));

			currentCardIndex = 0;
			updateDisplay();
		}

		function showSuggestions() {
			const query = searchInput?.value.toLowerCase() || '';
			if (query.length < 2) {
				hideSuggestions();
				return;
			}

			const suggestions = getSuggestions(query);
			if (suggestions.length === 0) {
				hideSuggestions();
				return;
			}

			if (searchSuggestions) {
				searchSuggestions.innerHTML = suggestions.map(suggestion => `
					<div class="px-4 py-2 hover:bg-shabby-pink cursor-pointer font-roboto text-sm" style="color: #333333;" onclick="selectSuggestion('${suggestion}')">
						${suggestion}
					</div>
				`).join('');
				searchSuggestions.classList.remove('hidden');
			}
		}

		function hideSuggestions() {
			searchSuggestions?.classList.add('hidden');
		}

		function getSuggestions(query: string): string[] {
			const suggestions: string[] = [];

			// Direct flower name matches
			flowers.forEach(flower => {
				if (flower.name.toLowerCase().includes(query)) {
					suggestions.push(flower.name);
				}
			});

			// Typo corrections - check if query matches any known typos
			Object.entries(commonTypos).forEach(([correct, typos]) => {
				// Check if the query matches any of the typos for this correct word
				if (typos.some(typo => typo.includes(query) || query.includes(typo))) {
					// Find flowers that contain the correct word
					flowers.forEach(flower => {
						if (flower.name.toLowerCase().includes(correct) && !suggestions.includes(flower.name)) {
							suggestions.push(flower.name);
						}
					});
				}
			});

			// Remove duplicates and limit to 5 suggestions
			return [...new Set(suggestions)].slice(0, 5);
		}

		function resetSearch() {
			if (searchInput) {
				searchInput.value = '';
			}
			searchClear?.classList.add('hidden');
			hideSuggestions();

			// Reset all filters
			if (colorFilter) colorFilter.value = '';
			if (occasionFilter) occasionFilter.value = '';

			applyFilters();
		}

		// Global function for suggestion selection
		(window as any).selectSuggestion = function(suggestion: string) {
			if (searchInput) {
				searchInput.value = suggestion;
			}
			hideSuggestions();
			applyFilters();
		};

		function switchView(view: string) {
			currentView = view;

			if (view === 'carousel') {
				carouselView?.classList.add('vintage-button');
				carouselView?.classList.remove('border-2');
				carouselView?.style.removeProperty('border-color');
				carouselView?.style.removeProperty('color');
				carouselView?.style.removeProperty('background-color');

				listView?.classList.remove('vintage-button');
				listView?.classList.add('border-2');
				listView?.style.setProperty('border-color', '#E8B4B8');
				listView?.style.setProperty('color', '#E8B4B8');
				listView?.style.setProperty('background-color', '#FAF7F4');

				mobileCarousel?.classList.remove('hidden');
				mobileList?.classList.add('hidden');
			} else {
				listView?.classList.add('vintage-button');
				listView?.classList.remove('border-2');
				listView?.style.removeProperty('border-color');
				listView?.style.removeProperty('color');
				listView?.style.removeProperty('background-color');

				carouselView?.classList.remove('vintage-button');
				carouselView?.classList.add('border-2');
				carouselView?.style.setProperty('border-color', '#E8B4B8');
				carouselView?.style.setProperty('color', '#E8B4B8');
				carouselView?.style.setProperty('background-color', '#FAF7F4');

				mobileList?.classList.remove('hidden');
				mobileCarousel?.classList.add('hidden');
			}

			updateDisplay();
		}

		function navigateCarousel(direction: number) {
			if (filteredFlowers.length === 0) return;

			// Circular navigation - wrap around when reaching ends
			currentCardIndex += direction;
			if (currentCardIndex >= filteredFlowers.length) {
				currentCardIndex = 0; // Loop back to start
			} else if (currentCardIndex < 0) {
				currentCardIndex = filteredFlowers.length - 1; // Loop to end
			}

			updateDisplay();
		}

		function updateDisplay() {
			const query = searchInput?.value.toLowerCase() || '';
			const hasSearchQuery = query.length > 0;
			const hasResults = filteredFlowers.length > 0;

			if (hasSearchQuery && !hasResults) {
				// Search query but no name matches - show only request form
				showNoResults();
				disableFiltersAndControls();
				return;
			}

			// Either no search query (show all) or search with results
			hideNoResults();
			enableFiltersAndControls();

			if (window.innerWidth >= 1024) {
				updateDesktopGrid();
			} else {
				if (currentView === 'carousel') {
					updateMobileCarousel();
				} else {
					updateMobileList();
				}
			}
		}

		function showNoResults() {
			// Remove all classes except 'hidden' to eliminate conflicts
			if (mobileCarousel) {
				mobileCarousel.className = 'hidden';
			}
			if (mobileList) {
				mobileList.className = 'hidden';
			}
			if (desktopGrid) {
				desktopGrid.className = 'hidden';
			}

			// Show the no results section
			noResults?.classList.remove('hidden');

			// Pre-fill the search term in the request form with exactly what user typed (including typos)
			const requestedFlowerInput = document.getElementById('requestedFlower') as HTMLInputElement;
			if (requestedFlowerInput && searchInput?.value) {
				requestedFlowerInput.value = searchInput.value; // Exact user input, typos included
			}
		}

		function hideNoResults() {
			noResults?.classList.add('hidden');

			// Restore original classes to all containers
			if (mobileCarousel && originalClasses.mobileCarousel) {
				mobileCarousel.className = originalClasses.mobileCarousel;
			}
			if (mobileList && originalClasses.mobileList) {
				mobileList.className = originalClasses.mobileList;
			}
			if (desktopGrid && originalClasses.desktopGrid) {
				desktopGrid.className = originalClasses.desktopGrid;
			}

			// Apply current view state
			if (window.innerWidth >= 1024) {
				desktopGrid?.classList.remove('hidden');
				mobileCarousel?.classList.add('hidden');
				mobileList?.classList.add('hidden');
			} else {
				desktopGrid?.classList.add('hidden');
				if (currentView === 'carousel') {
					mobileCarousel?.classList.remove('hidden');
					mobileList?.classList.add('hidden');
				} else {
					mobileList?.classList.remove('hidden');
					mobileCarousel?.classList.add('hidden');
				}
			}
		}

		function disableFiltersAndControls() {
			// Disable filters (but keep them visible)
			if (colorFilter) {
				colorFilter.disabled = true;
				colorFilter.style.opacity = '0.5';
				colorFilter.style.cursor = 'not-allowed';
			}
			if (occasionFilter) {
				occasionFilter.disabled = true;
				occasionFilter.style.opacity = '0.5';
				occasionFilter.style.cursor = 'not-allowed';
			}

			// Disable view toggle buttons (but keep them visible)
			if (carouselView) {
				carouselView.disabled = true;
				carouselView.style.opacity = '0.5';
				carouselView.style.cursor = 'not-allowed';
			}
			if (listView) {
				listView.disabled = true;
				listView.style.opacity = '0.5';
				listView.style.cursor = 'not-allowed';
			}
		}

		function enableFiltersAndControls() {
			// Enable filters
			if (colorFilter) {
				colorFilter.disabled = false;
				colorFilter.style.opacity = '1';
				colorFilter.style.cursor = 'pointer';
			}
			if (occasionFilter) {
				occasionFilter.disabled = false;
				occasionFilter.style.opacity = '1';
				occasionFilter.style.cursor = 'pointer';
			}

			// Enable view toggle buttons
			if (carouselView) {
				carouselView.disabled = false;
				carouselView.style.opacity = '1';
				carouselView.style.cursor = 'pointer';
			}
			if (listView) {
				listView.disabled = false;
				listView.style.opacity = '1';
				listView.style.cursor = 'pointer';
			}
		}

		function createFlowerCard(flower: any, isDesktop = false) {
			const cardClass = isDesktop ?
				'shabby-card p-4 hover:scale-105 transition-transform duration-300 cursor-pointer' :
				'shabby-card p-4 flex-shrink-0 w-80 sm:w-72 snap-center min-h-80';

			return `
				<div class="${cardClass}">
					<div class="relative mb-4">
						<div class="w-full h-48 flex items-center justify-center overflow-hidden">
							<img class="w-full h-full object-cover" src="./src/assets/images/flowers/${flower.image}" alt="${flower.name}" />
						</div>
					</div>
					<div>
						<h3 class="text-lg font-playfair font-bold mb-2" style="color: #333333;">${flower.name}</h3>
						<p class="text-xs font-roboto mb-3" style="color: #333333;">${flower.description}</p>
						<div class="flex flex-wrap gap-1">
							${flower.colors.map((color: string) => `
								<span class="px-2 py-1 text-xs px-2 py-1 font-roboto" style="color: #333333; border: 1px solid #333;">
									${color}
								</span>
							`).join('')}
						</div>
					</div>
				</div>
			`;
		}

		function createListItem(flower: any) {
			return `
				<div class="shabby-card p-4 distressed-shadow flex items-center space-x-4">
					<div class="w-20 h-20 rounded-lg flex items-center justify-center vintage-border flex-shrink-0" style="background: linear-gradient(135deg, #F8E7E8 0%, #E8E0F0 100%);">
						<svg class="w-8 h-8" style="color: #E8B4B8;" fill="currentColor" viewBox="0 0 20 20">
							<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
						</svg>
					</div>
					<div class="flex-1">
						<h3 class="text-lg font-playfair font-bold mb-1" style="color: #333333;">${flower.name}</h3>
						<p class="text-sm font-roboto mb-2" style="color: #333333;">${flower.description}</p>
						<div class="flex flex-wrap gap-1">
							${flower.colors.slice(0, 3).map((color: string) => `
								<span class="px-2 py-1 rounded-full text-xs font-roboto" style="background-color: #FAF7F4; color: #333333; border: 1px solid #E8B4B8;">
									${color}
								</span>
							`).join('')}
							${flower.colors.length > 3 ? `<span class="text-xs font-roboto" style="color: #E8B4B8;">+${flower.colors.length - 3} more</span>` : ''}
						</div>
					</div>

				</div>
			`;
		}

		function updateMobileCarousel() {
			if (!carouselTrack) return;

			// Show all filtered flowers in carousel
			carouselTrack.innerHTML = filteredFlowers.map(flower => createFlowerCard(flower)).join('');

			// Scroll to current card position
			const carouselContainer = mobileCarousel?.querySelector('.overflow-x-auto');
			if (carouselContainer && filteredFlowers.length > 0) {
				const cardWidth = 336; // w-80 (320px) + gap-4 (16px) = 336px
				const scrollPosition = currentCardIndex * cardWidth;
				carouselContainer.scrollTo({
					left: scrollPosition,
					behavior: 'smooth'
				});
			}

			updateCarouselControls();
			updateFlowerNavigation();
		}

		function updateMobileList() {
			if (!listContainer) return;
			listContainer.innerHTML = filteredFlowers.map(flower => createListItem(flower)).join('');
		}

		function updateDesktopGrid() {
			if (!desktopGrid) return;
			desktopGrid.innerHTML = filteredFlowers.map(flower => createFlowerCard(flower, true)).join('');
		}

		function updateCarouselControls() {
			// For circular navigation, buttons are always enabled when there are flowers
			const hasFlowers = filteredFlowers.length > 0;

			if (prevBtn) {
				prevBtn.disabled = !hasFlowers;
				prevBtn.style.opacity = hasFlowers ? '1' : '0.5';
			}

			if (nextBtn) {
				nextBtn.disabled = !hasFlowers;
				nextBtn.style.opacity = hasFlowers ? '1' : '0.5';
			}
		}

		function updateFlowerNavigation() {
			if (!flowerNavText || !flowerNavList) return;

			const totalFlowers = filteredFlowers.length;
			const currentFlower = filteredFlowers[currentCardIndex];

			// Update the button text
			if (totalFlowers > 0 && currentFlower) {
				flowerNavText.textContent = `${currentFlower.name} (${currentCardIndex + 1} of ${totalFlowers})`;
			} else {
				flowerNavText.textContent = '0 of 0';
			}

			// Update the dropdown list
			flowerNavList.innerHTML = '';
			filteredFlowers.forEach((flower, index) => {
				const item = document.createElement('button');
				item.className = `w-full text-left px-3 py-2 rounded font-roboto text-sm transition-colors duration-200 ${
					index === currentCardIndex
						? 'font-semibold'
						: 'hover:bg-shabby-pink'
				}`;
				item.style.backgroundColor = index === currentCardIndex ? '#F8E7E8' : 'transparent';
				item.style.color = '#333333';
				item.textContent = `${index + 1}. ${flower.name}`;
				item.setAttribute('aria-label', `Go to ${flower.name}`);

				item.addEventListener('click', () => {
					currentCardIndex = index;
					hideFlowerNavDropdown();
					updateDisplay();
				});

				flowerNavList.appendChild(item);
			});
		}

		function toggleFlowerNavDropdown() {
			if (!flowerNavDropdown) return;

			if (flowerNavDropdown.classList.contains('hidden')) {
				showFlowerNavDropdown();
			} else {
				hideFlowerNavDropdown();
			}
		}

		function showFlowerNavDropdown() {
			flowerNavDropdown?.classList.remove('hidden');
		}

		function hideFlowerNavDropdown() {
			flowerNavDropdown?.classList.add('hidden');
		}

		function handleFlowerRequest(e: Event) {
			e.preventDefault();

			const formData = new FormData(flowerRequestForm);
			const requestData = {
				flower: formData.get('requestedFlower'),
				email: formData.get('requesterEmail'),
				notes: formData.get('requestNotes')
			};

			// Simulate form submission
			alert(`Thank you for your flower request!\n\nFlower: ${requestData.flower}\nEmail: ${requestData.email}\n\nWe'll get back to you soon about availability.`);

			flowerRequestForm.reset();
		}

		// Handle window resize
		window.addEventListener('resize', debounce(() => {
			updateDisplay();
		}, 250));
	}
</script>

<!-- Modern modular approach for better performance -->
<script type="module" is:inline>
	// Import modular functionality for better code splitting
	// Note: This is a progressive enhancement - the legacy code above provides fallback

	// Lazy load modules when needed
	async function initializeModernFeatures() {
		try {
			// Only load if modules are supported
			if ('noModule' in HTMLScriptElement.prototype) return;

			// Dynamic imports for code splitting
			const [{ BookingManager }, { FlowerManager }] = await Promise.all([
				import('../scripts/booking.js'),
				import('../scripts/flowers.js')
			]);

			// Initialize modern implementations
			new BookingManager();
			new FlowerManager();

			console.log('Modern modular features loaded');
		} catch (error) {
			console.log('Falling back to legacy implementation:', error);
		}
	}

	// Initialize when DOM is ready
	if (document.readyState === 'loading') {
		document.addEventListener('DOMContentLoaded', initializeModernFeatures);
	} else {
		initializeModernFeatures();
	}
</script>
