---
import "../styles/global.css";
import Navigation from "../components/Navigation.astro";

export interface Props {
  title?: string;
}

const { title = "Bouquet Garden" } = Astro.props;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
	</head>
	<body>
		<Navigation />
		<main class="pt-16">
			<slot />
		</main>
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
	}

	/* Hide scrollbar for carousel */
	.scrollbar-hide {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}
	.scrollbar-hide::-webkit-scrollbar {
		display: none;
	}

	/* Smooth touch scrolling */
	.touch-scroll {
		-webkit-overflow-scrolling: touch;
		overscroll-behavior-x: contain;
	}

	/* Center mobile text utility */
	.center-mobile-text {
		text-align: center;
	}

	@media (min-width: 768px) {
		.center-mobile-text {
			text-align: left;
		}
	}

	/* Flower navigation dropdown */
	.hover\:bg-shabby-pink:hover {
		background-color: #F8E7E8;
	}
</style>
